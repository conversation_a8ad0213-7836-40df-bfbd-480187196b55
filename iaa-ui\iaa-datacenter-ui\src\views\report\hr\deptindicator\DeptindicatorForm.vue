<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="150px"
      v-loading="formLoading"
    >
      <el-form-item label="部门" prop="dept">
        <el-select v-model="formData.dept" placeholder="请选择部门" @change="deptChange">
          <el-option
            v-for="dict in getStrDictOptions('first_level_department')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="年" prop="year">
        <el-input v-model="formData.year" placeholder="请输入年" :disabled="true" />
      </el-form-item>
      <el-form-item label="月" prop="month">
        <el-input v-model="formData.month" placeholder="请输入月" :disabled="true" />
      </el-form-item>
      <!-- <el-form-item label="指标名称" prop="indicator">
        <el-select v-model="formData.indicator" placeholder="请选择指标名称">
          <el-option
            v-for="dict in getStrDictOptions(formData.dept + '_Indicator')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="指标值" prop="value">
        <el-input-number v-model="formData.value" placeholder="请输入指标值" />
      </el-form-item> -->
      <el-form-item
        v-for="dict in getStrDictOptions(formData.dept + '_Indicator')"
        :key="dict.value"
        :label="dict.label"
      >
        <el-input-number
          v-model="formData.indicators[dict.value]"
          class="!w-100%"
          placeholder="请输入指标值"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions } from '@/utils/dict'
import { DeptIndicatorApi, DeptIndicatorVO } from '@/api/hr/deptindicator'
import { dateUtil } from '@/utils/dateUtil'

/** 部门指标 表单 */
defineOptions({ name: 'DeptIndicatorForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  dept: undefined,
  year: undefined,
  month: undefined,
  indicators: undefined as any
})
const formRules = reactive({
  dept: [{ required: true, message: '部门不能为空', trigger: 'change' }],
  year: [{ required: true, message: '年不能为空', trigger: 'blur' }],
  month: [{ required: true, message: '月不能为空', trigger: 'blur' }]
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, date: string, dept?: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  formData.value.year = dateUtil(date).format('YYYY') as unknown as undefined
  formData.value.month = dateUtil(date).format('M') as unknown as undefined
  // 修改时，设置数据
  if (dept) {
    formLoading.value = true
    try {
      const res = await DeptIndicatorApi.getDeptIndicator({
        dept,
        date
      })

      formData.value.indicators = res.indicators.reduce((acc: Record<string, any>, cur: any) => {
        acc[cur.indicator] = cur.value
        return acc
      }, {})
      formData.value.dept = res.dept
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeptIndicatorVO
    await DeptIndicatorApi.saveDeptIndicator(data)
    message.success('保存成功')
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

const deptChange = async () => {
  formData.value.indicators = {} as any
  if (!formData.value.dept) return
  const res = await DeptIndicatorApi.getDeptIndicator({
    dept: formData.value.dept,
    date: `${formData.value.year}-${formData.value.month! < 10 ? '0' : ''}${formData.value.month}-01`
  })
  if (!res?.indicators) {
    getStrDictOptions(formData.value.dept + '_Indicator').forEach((item) => {
      formData.value.indicators[item.value] = 0
    })
  } else {
    formData.value.indicators = res?.indicators?.reduce((acc: Record<string, any>, cur: any) => {
      acc[cur.indicator] = cur.value
      return acc
    }, {})
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    dept: undefined,
    year: undefined,
    month: undefined,
    indicators: undefined
  }
  formRef.value?.resetFields()
}
</script>
