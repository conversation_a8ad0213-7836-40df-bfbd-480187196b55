<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="时间">
        <el-date-picker
          v-model="queryParams.date"
          value-format="YYYY-MM-DD"
          type="month"
          :clearable="false"
          @change="getList"
        />
      </el-form-item>
      <el-form-item label="部门" prop="dept">
        <el-select v-model="queryParams.dept" placeholder="请选择部门" clearable class="!w-240px">
          <el-option
            v-for="dict in getStrDictOptions('first_level_department')"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getList"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['hr:dept-indicator:create']"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table
      v-loading="loading"
      :data="list"
      :stripe="true"
      :show-overflow-tooltip="true"
      height="calc(100vh - 220px)"
      :row-class-name="tableRowClassName"
      default-expand-all
    >
      <el-table-column type="expand">
        <template #default="{ row }">
          <div class="w-70% mx-auto">
            <el-form inline label-position="top" class="custom-form" label-width="200px">
              <el-form-item
                v-for="indicator in row.indicators"
                :key="indicator.value"
                :label="getDictLabel(row.dept + '_Indicator', indicator.indicator)"
              >
                {{ indicator.value }}
              </el-form-item>
            </el-form>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="部门" align="center" prop="dept">
        <template #default="scope">
          <dict-tag type="first_level_department" :value="scope.row.dept" />
        </template>
      </el-table-column>
      <el-table-column label="年" align="center" prop="year" />
      <el-table-column label="月" align="center" prop="month" />
      <el-table-column label="操作" align="center" min-width="120px">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.dept)"
            v-hasPermi="['hr:dept-indicator:update']"
          >
            编辑
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row)"
            v-hasPermi="['hr:dept-indicator:delete']"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <DeptIndicatorForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getDictLabel, getStrDictOptions } from '@/utils/dict'
import { DeptIndicatorApi, DeptIndicatorVO } from '@/api/hr/deptindicator'
import DeptIndicatorForm from './DeptIndicatorForm.vue'
import { dateUtil } from '@/utils/dateUtil'

/** 部门指标 列表 */
defineOptions({ name: 'DeptIndicator' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<DeptIndicatorVO[]>([]) // 列表的数据
const queryParams = reactive({
  dept: undefined,
  indicator: undefined,
  date: dateUtil().format('YYYY-MM-DD')
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await DeptIndicatorApi.getDeptIndicatorPage(queryParams)
    list.value = data
  } finally {
    loading.value = false
  }
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  getList()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, queryParams.date, id)
}

/** 删除按钮操作 */
const handleDelete = async (row: any) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await DeptIndicatorApi.deleteDeptIndicator({
      dept: row.dept,
      year: row.year,
      month: row.month
    })
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 初始化 **/
onMounted(() => {
  getList()
})

const tableRowClassName = ({ rowIndex }: { rowIndex: number }) => {
  if (rowIndex % 2 === 0) {
    return 'even-row'
  } else {
    return 'odd-row'
  }
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item__content),
:deep(.el-form-item__label) {
  align-items: center;
  justify-content: center;
}

:deep(.even-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
:deep(.odd-row) {
  --el-table-tr-bg-color: var(--el-color-success-light-7);
}

:deep(.custom-form .el-form-item) {
  margin-bottom: 0;
  border-radius: 10px;
  padding: 5px;
  background-color: var(--el-color-primary-light-9);
  width: 20%;
  text-align: center;
}

.custom-form {
  justify-content: space-around;
}
</style>
